.main-container-top {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header1 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 30px;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

.configuration-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 25px;
  margin-bottom: 20px;
}

.dynamic-item {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 20px;
  background-color: #ffffff;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.header {
  font-size: 1.25rem;
  font-weight: 600;
  color: #34495e;
  margin-bottom: 20px;
  padding: 10px 15px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 5px;
  text-align: center;
}

.header-row {
  background-color: #f8f9fa;
  padding: 10px 15px;
  border-radius: 5px;
  border: 1px solid #dee2e6;

  .form-label {
    margin-bottom: 0;
    color: #495057;
    font-size: 0.9rem;
  }
}

.parameter-row {
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  align-items: center;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:last-child {
    border-bottom: none;
  }
}

// Form Controls
.form-control {
  border: 1px solid #ced4da;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

  &:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }

  &:read-only {
    background-color: #f8f9fa;
    opacity: 1;
  }

  &:disabled {
    background-color: #e9ecef;
    opacity: 0.6;
  }
}

.form-range {
  width: 100%;
  margin: 10px 0;
}

// Material Form Fields
.mat-form-field {
  width: 100%;
  
  .mat-select {
    font-size: 0.9rem;
  }
}

// File Upload Section
.file-upload-section {
  .upload-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    }

    &:disabled {
      background: #6c757d;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      font-size: 18px;
      width: 18px;
      height: 18px;
    }
  }

  .file-preview {
    .preview-image {
      border: 2px solid #dee2e6;
      border-radius: 5px;
      max-width: 100%;
      height: auto;
    }

    .file-name {
      font-size: 0.85rem;
      color: #6c757d;
      margin-top: 5px;
      word-break: break-all;
    }
  }
}

// Parameter Description
.parameter-description {
  font-size: 0.9rem;
  color: #6c757d;
  line-height: 1.4;
  margin: 0;
  padding: 8px 0;
}

// Action Section
.action-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  text-align: right;

  .btn {
    padding: 10px 25px;
    font-size: 0.95rem;
    font-weight: 500;
    border-radius: 5px;
    transition: all 0.3s ease;

    &.btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }

      &:disabled {
        background: #6c757d;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }

    &.btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;

      &:hover {
        background-color: #5a6268;
        border-color: #545b62;
      }
    }
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}

// Input Group Styling
.input-group {
  .form-text {
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-left: none;
    padding: 8px 12px;
    border-radius: 0 4px 4px 0;
    font-size: 0.85rem;
    color: #495057;
    min-width: 60px;
    text-align: center;
  }
}

// Alert Styling
.alert {
  font-size: 0.85rem;
  padding: 8px 12px;
  margin-bottom: 0;
  border-radius: 4px;

  &.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
  }
}

// Responsive Design
@media (max-width: 768px) {
  .main-container-top {
    padding: 15px;
  }

  .configuration-section {
    padding: 15px;
  }

  .parameter-row {
    padding: 10px 5px;
  }

  .header1 {
    font-size: 1.25rem;
  }

  .header {
    font-size: 1.1rem;
    padding: 8px 12px;
  }

  .action-section {
    text-align: center;
    
    .btn {
      width: 100%;
      margin-bottom: 10px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Accessibility Improvements
.form-control:focus,
.mat-select:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

// Loading State
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}
