<div class="container-fluid main-container-top" style="width: 100%; margin-top: 30px;">    
  <p class="header1">Plant - {{ SelectedPlantName }}</p>
  
  <div class="configuration-section">
    <form [formGroup]="recordForm" novalidate>
      <div *ngFor="let category of categories; trackBy: trackByCategory" 
           class="dynamic-item mb-4">
        
        <p class="header">{{ category }}</p>
        
        <!-- Header Row -->
        <div class="row header-row mb-2">
          <div class="col-md-3 col-sm-3 col-12">
            <label class="form-label fw-bold">
              {{ Caption.CAPTParamConfig002 | translate }}
            </label>
          </div>
          <div class="col-md-3 col-sm-3 col-12">
            <label class="form-label fw-bold">
              {{ Caption.CAPTParamConfig004 | translate }}
            </label>
          </div>
          <div class="col-md-6 col-sm-6 col-12">
            <label class="form-label fw-bold">
              {{ Caption.CAPTParamConfig003 | translate }}
            </label>
          </div>
        </div>

        <!-- Parameter Rows -->
        <div *ngFor="let record of getRecordsByCategory(category); trackBy: trackByRecord; let i = index" 
             [formGroup]="record" 
             class="row parameter-row mb-3">
          
          <!-- Parameter Name -->
          <div class="col-md-3 col-sm-3 col-12">
            <input 
              readonly 
              formControlName="PARAM_DISPLAY_NAME" 
              [id]="'param_name_' + category + '_' + i"
              [attr.aria-label]="'Parameter name for ' + record.value.PARAM_DISPLAY_NAME"
              class="form-control" />
          </div>
          
          <!-- Parameter Value - TEXT Type -->
          <div class="col-md-3 col-sm-3 col-12" 
               *ngIf="record.value.PARAM_DISPLAY_TYPE === 'TEXT'">
            
            <!-- Integer Input -->
            <div *ngIf="record.value.PARAM_TYPE === 'INT'" class="input-group">
              <input 
                *ngIf="enableEdit" 
                readonly 
                [disabled]="enableEdit"   
                [min]="getMin(record)" 
                [max]="record.value.PARAM_MAX_VALUE" 
                formControlName="PARAM_VALUE" 
                [id]="'param_value_int_' + category + '_' + i"
                [attr.aria-label]="'Integer value for ' + record.value.PARAM_DISPLAY_NAME"
                class="form-control"
                type="number">
              
              <input 
                *ngIf="!enableEdit" 
                [disabled]="enableEdit" 
                type="range"  
                [min]="getMin(record)" 
                [max]="record.value.PARAM_MAX_VALUE" 
                formControlName="PARAM_VALUE" 
                [id]="'param_value_range_' + category + '_' + i"
                [attr.aria-label]="'Range value for ' + record.value.PARAM_DISPLAY_NAME"
                class="form-range">
              
              <output *ngIf="!enableEdit" class="form-text">
                {{ record.value.PARAM_VALUE }}
              </output>
            </div>
            
            <!-- Text Input -->
            <div *ngIf="record.value.PARAM_TYPE !== 'INT'">
              <input 
                type="text"  
                [disabled]="enableEdit" 
                formControlName="PARAM_VALUE" 
                [id]="'param_value_text_' + category + '_' + i"
                [attr.aria-label]="'Text value for ' + record.value.PARAM_DISPLAY_NAME"
                class="form-control">
            </div>
          </div>

          <!-- Parameter Value - DROPDOWN Type -->
          <div class="col-md-3 col-sm-3 col-12" 
               *ngIf="record.value.PARAM_DISPLAY_TYPE === 'DROPDOWN'">
            <mat-form-field appearance="outline" class="w-100">
              <mat-select  
                formControlName="PARAM_VALUE" 
                [id]="'param_value_dropdown_' + category + '_' + i"
                [compareWith]="compareValue"
                [attr.aria-label]="'Dropdown value for ' + record.value.PARAM_DISPLAY_NAME">
                <mat-option 
                  [disabled]="enableEdit"  
                  *ngFor="let option of record.value.options; trackBy: trackByOption" 
                  [value]="option.id">
                  {{ option.name }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <!-- Parameter Value - FILE Type -->
          <div class="col-md-3 col-sm-3 col-12" 
               *ngIf="record.value.PARAM_DISPLAY_TYPE === 'FILE'">
            <div class="file-upload-section">
              <input 
                type="hidden"   
                formControlName="PARAM_VALUE" 
                [id]="'param_value_hidden_' + category + '_' + i"
                value="CustomerLogo">

              <button 
                [disabled]="enableEdit" 
                mat-raised-button 
                (click)="fileInputs.get(category + '_' + i)?.click()" 
                title="Maximum Size 20MB"
                type="button"
                class="upload-btn">
                <mat-icon>arrow_upward</mat-icon>
                <span>{{ Caption.CAPTUserSetup0111 | translate }}</span>
              </button>

              <input 
                type="file" 
                [id]="'file_input_' + category + '_' + i"
                formControlName="PARAM_UPLOADED_PIC"
                style="display: none;" 
                (change)="onFileChange($event, category, i)" 
                #fileInput
                accept="image/*"
                [attr.aria-label]="'File upload for ' + record.value.PARAM_DISPLAY_NAME">

              <div class="file-preview mt-2" *ngIf="getImageUrl(category, i)">
                <img 
                  [src]="getImageUrl(category, i)" 
                  [alt]="'Preview for ' + record.value.PARAM_DISPLAY_NAME"
                  class="preview-image"
                  height="100">
                <div class="file-name">{{ getImageName(category, i) }}</div>
              </div>

              <div class="alert alert-danger mt-2" 
                   *ngIf="getImageError(category, i)"
                   role="alert">
                {{ getImageError(category, i) }}
              </div>
            </div>
          </div>

          <!-- Parameter Description -->
          <div class="col-md-6 col-sm-6 col-12">
            <p class="parameter-description">{{ record.value.PARAM_DESC }}</p>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Action Buttons -->
  <div class="action-section mt-4">
    <button 
      type="button"
      class="btn btn-primary"
      [disabled]="recordForm.invalid || isSubmitting"
      (click)="onSubmit()">
      <span *ngIf="isSubmitting" class="spinner-border spinner-border-sm me-2" role="status"></span>
      {{ submitButtonText | translate }}
    </button>
    
    <button 
      type="button"
      class="btn btn-secondary ms-2"
      (click)="onReset()">
      {{ 'Reset' | translate }}
    </button>
  </div>
</div>
