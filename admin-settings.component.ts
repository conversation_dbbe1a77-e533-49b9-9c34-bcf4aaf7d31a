import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, FormArray, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

export interface ParameterOption {
  id: string | number;
  name: string;
}

export interface ParameterRecord {
  PARAM_NAME: string;
  PARAM_DISPLAY_NAME: string;
  PARAM_DESC: string;
  PARAM_VALUE: any;
  PARAM_TYPE: 'INT' | 'TEXT' | 'BOOLEAN';
  PARAM_DISPLAY_TYPE: 'TEXT' | 'DROPDOWN' | 'FILE' | 'SLIDER';
  PARAM_MAX_VALUE?: number;
  PARAM_MIN_VALUE?: number;
  options?: ParameterOption[];
  category: string;
}

export interface FileUploadState {
  url?: string;
  name?: string;
  error?: string;
}

@Component({
  selector: 'app-admin-settings',
  templateUrl: './admin-settings.component.html',
  styleUrls: ['./admin-settings.component.scss']
})
export class AdminSettingsComponent implements OnInit, OnDestroy {
  recordForm: FormGroup;
  categories: string[] = [];
  records: ParameterRecord[] = [];
  enableEdit = false;
  isSubmitting = false;
  
  // File upload tracking
  fileUploadStates = new Map<string, FileUploadState>();
  fileInputs = new Map<string, HTMLInputElement>();
  
  // Component properties
  SelectedPlantName = '';
  submitButtonText = 'Save Configuration';
  
  // Caption/Translation object
  Caption: any = {};
  
  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    // Add your services here
  ) {
    this.recordForm = this.fb.group({});
  }

  ngOnInit(): void {
    this.initializeForm();
    this.loadData();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    // Initialize form structure based on records
    this.recordForm = this.fb.group({});
  }

  private loadData(): void {
    // Load parameter data and build form
    // This should be replaced with actual service calls
  }

  // TrackBy functions for performance
  trackByCategory(index: number, category: string): string {
    return category;
  }

  trackByRecord(index: number, record: any): string {
    return record.value?.PARAM_NAME || index.toString();
  }

  trackByOption(index: number, option: ParameterOption): string | number {
    return option.id;
  }

  // Get records filtered by category
  getRecordsByCategory(category: string): FormGroup[] {
    // Return form groups for records in this category
    return [];
  }

  // Get minimum value for numeric inputs
  getMin(record: any): number {
    return record.value?.PARAM_MIN_VALUE || 0;
  }

  // Compare function for mat-select
  compareValue(option1: any, option2: any): boolean {
    return option1 === option2;
  }

  // File upload handling
  onFileChange(event: Event, category: string, index: number): void {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    const key = `${category}_${index}`;
    
    if (!file) {
      this.clearFileUploadState(key);
      return;
    }

    // Validate file size (20MB limit)
    const maxSize = 20 * 1024 * 1024; // 20MB in bytes
    if (file.size > maxSize) {
      this.setFileUploadError(key, 'File size exceeds 20MB limit');
      return;
    }

    // Validate file type
    if (!file.type.startsWith('image/')) {
      this.setFileUploadError(key, 'Please select a valid image file');
      return;
    }

    // Create preview URL
    const reader = new FileReader();
    reader.onload = (e) => {
      const url = e.target?.result as string;
      this.setFileUploadState(key, {
        url,
        name: file.name,
        error: undefined
      });
    };
    reader.readAsDataURL(file);
  }

  private setFileUploadState(key: string, state: FileUploadState): void {
    this.fileUploadStates.set(key, state);
  }

  private setFileUploadError(key: string, error: string): void {
    this.fileUploadStates.set(key, { error });
  }

  private clearFileUploadState(key: string): void {
    this.fileUploadStates.delete(key);
  }

  getImageUrl(category: string, index: number): string | undefined {
    return this.fileUploadStates.get(`${category}_${index}`)?.url;
  }

  getImageName(category: string, index: number): string | undefined {
    return this.fileUploadStates.get(`${category}_${index}`)?.name;
  }

  getImageError(category: string, index: number): string | undefined {
    return this.fileUploadStates.get(`${category}_${index}`)?.error;
  }

  // Form submission
  onSubmit(): void {
    if (this.recordForm.valid && !this.isSubmitting) {
      this.isSubmitting = true;
      
      // Process form data
      const formData = this.recordForm.value;
      
      // Call your service to save data
      // this.parameterService.saveConfiguration(formData)
      //   .pipe(takeUntil(this.destroy$))
      //   .subscribe({
      //     next: (response) => {
      //       // Handle success
      //       this.isSubmitting = false;
      //     },
      //     error: (error) => {
      //       // Handle error
      //       this.isSubmitting = false;
      //     }
      //   });
    }
  }

  // Reset form
  onReset(): void {
    this.recordForm.reset();
    this.fileUploadStates.clear();
  }
}
